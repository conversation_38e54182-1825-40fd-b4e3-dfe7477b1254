/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../../../../index";
export interface UpdateSquadDto {
    /** This is the name of the squad. */
    name?: string;
    /**
     * This is the list of assistants that make up the squad.
     *
     * The call will start with the first assistant in the list.
     */
    members: Vapi.SquadMemberDto[];
    /**
     * This can be used to override all the assistants' settings and provide values for their template variables.
     *
     * Both `membersOverrides` and `members[n].assistantOverrides` can be used together. First, `members[n].assistantOverrides` is applied. Then, `membersOverrides` is applied as a global override.
     */
    membersOverrides?: Vapi.AssistantOverrides;
}
