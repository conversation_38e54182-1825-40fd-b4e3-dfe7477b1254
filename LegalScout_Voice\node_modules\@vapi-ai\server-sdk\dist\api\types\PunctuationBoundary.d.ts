/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type PunctuationBoundary = 
/**
 * 。 */
"\u3002"
/**
 * ， */
 | "\uFF0C"
/**
 * . */
 | "."
/**
 * ! */
 | "!"
/**
 * ? */
 | "?"
/**
 * ; */
 | ";"
/**
 * ) */
 | ")"
/**
 * ، */
 | "\u060C"
/**
 * ۔ */
 | "\u06D4"
/**
 * । */
 | "\u0964"
/**
 * ॥ */
 | "\u0965"
/**
 * | */
 | "|"
/**
 * || */
 | "||"
/**
 * , */
 | ","
/**
 * : */
 | ":";
export declare const PunctuationBoundary: {
    readonly Circle: "。";
    readonly FullWidthComma: "，";
    readonly Dot: ".";
    readonly Exclamation: "!";
    readonly Question: "?";
    readonly Semicolon: ";";
    readonly Parenthesis: ")";
    readonly ArabicComma: "،";
    readonly UrduFullStop: "۔";
    readonly BengaliFullStop: "।";
    readonly DoubleDanda: "॥";
    readonly Pipe: "|";
    readonly DoublePipe: "||";
    readonly HalfWidthComma: ",";
    readonly Colon: ":";
};
