/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../../../index";
/**
 * These are the settings to configure or disable voicemail detection. Alternatively, voicemail detection can be configured using the model.tools=[VoicemailTool].
 * This uses <PERSON><PERSON><PERSON>'s built-in detection while the VoicemailTool relies on the model to detect if a voicemail was reached.
 * You can use neither of them, one of them, or both of them. By default, Twilio built-in detection is enabled while VoicemailTool is not.
 */
export type UpdateAssistantDtoVoicemailDetection = Vapi.GoogleVoicemailDetectionPlan | Vapi.OpenAiVoicemailDetectionPlan | Vapi.TwilioVoicemailDetectionPlan;
