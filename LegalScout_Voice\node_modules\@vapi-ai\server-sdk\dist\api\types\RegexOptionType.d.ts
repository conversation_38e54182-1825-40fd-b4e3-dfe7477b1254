/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the type of the regex option. Options are:
 * - `ignore-case`: Ignores the case of the text being matched. Add
 * - `whole-word`: Matches whole words only.
 * - `multi-line`: Matches across multiple lines.
 */
export type RegexOptionType = "ignore-case" | "whole-word" | "multi-line";
export declare const RegexOptionType: {
    readonly IgnoreCase: "ignore-case";
    readonly WholeWord: "whole-word";
    readonly MultiLine: "multi-line";
};
