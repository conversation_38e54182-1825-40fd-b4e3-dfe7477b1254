/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type RimeAiVoiceIdEnum = "abbie" | "allison" | "ally" | "alona" | "amber" | "ana" | "antoine" | "armon" | "brenda" | "brittany" | "carol" | "colin" | "courtney" | "elena" | "elliot" | "eva" | "geoff" | "gerald" | "hank" | "helen" | "hera" | "jen" | "joe" | "joy" | "juan" | "kendra" | "kendrick" | "kenneth" | "kevin" | "kris" | "linda" | "madison" | "marge" | "marina" | "marissa" | "marta" | "maya" | "nicholas" | "nyles" | "phil" | "reba" | "rex" | "rick" | "ritu" | "rob" | "rodney" | "rohan" | "rosco" | "samantha" | "sandy" | "selena" | "seth" | "sharon" | "stan" | "tamra" | "tanya" | "tibur" | "tj" | "tyler" | "viv" | "yadira" | "marsh" | "bayou" | "creek" | "brook" | "flower" | "spore" | "glacier" | "gulch" | "alpine" | "cove" | "lagoon" | "tundra" | "steppe" | "mesa" | "grove" | "rainforest" | "moraine" | "wildflower" | "peak" | "boulder" | "gypsum" | "zest";
export declare const RimeAiVoiceIdEnum: {
    readonly Abbie: "abbie";
    readonly Allison: "allison";
    readonly Ally: "ally";
    readonly Alona: "alona";
    readonly Amber: "amber";
    readonly Ana: "ana";
    readonly Antoine: "antoine";
    readonly Armon: "armon";
    readonly Brenda: "brenda";
    readonly Brittany: "brittany";
    readonly Carol: "carol";
    readonly Colin: "colin";
    readonly Courtney: "courtney";
    readonly Elena: "elena";
    readonly Elliot: "elliot";
    readonly Eva: "eva";
    readonly Geoff: "geoff";
    readonly Gerald: "gerald";
    readonly Hank: "hank";
    readonly Helen: "helen";
    readonly Hera: "hera";
    readonly Jen: "jen";
    readonly Joe: "joe";
    readonly Joy: "joy";
    readonly Juan: "juan";
    readonly Kendra: "kendra";
    readonly Kendrick: "kendrick";
    readonly Kenneth: "kenneth";
    readonly Kevin: "kevin";
    readonly Kris: "kris";
    readonly Linda: "linda";
    readonly Madison: "madison";
    readonly Marge: "marge";
    readonly Marina: "marina";
    readonly Marissa: "marissa";
    readonly Marta: "marta";
    readonly Maya: "maya";
    readonly Nicholas: "nicholas";
    readonly Nyles: "nyles";
    readonly Phil: "phil";
    readonly Reba: "reba";
    readonly Rex: "rex";
    readonly Rick: "rick";
    readonly Ritu: "ritu";
    readonly Rob: "rob";
    readonly Rodney: "rodney";
    readonly Rohan: "rohan";
    readonly Rosco: "rosco";
    readonly Samantha: "samantha";
    readonly Sandy: "sandy";
    readonly Selena: "selena";
    readonly Seth: "seth";
    readonly Sharon: "sharon";
    readonly Stan: "stan";
    readonly Tamra: "tamra";
    readonly Tanya: "tanya";
    readonly Tibur: "tibur";
    readonly Tj: "tj";
    readonly Tyler: "tyler";
    readonly Viv: "viv";
    readonly Yadira: "yadira";
    readonly Marsh: "marsh";
    readonly Bayou: "bayou";
    readonly Creek: "creek";
    readonly Brook: "brook";
    readonly Flower: "flower";
    readonly Spore: "spore";
    readonly Glacier: "glacier";
    readonly Gulch: "gulch";
    readonly Alpine: "alpine";
    readonly Cove: "cove";
    readonly Lagoon: "lagoon";
    readonly Tundra: "tundra";
    readonly Steppe: "steppe";
    readonly Mesa: "mesa";
    readonly Grove: "grove";
    readonly Rainforest: "rainforest";
    readonly Moraine: "moraine";
    readonly Wildflower: "wildflower";
    readonly Peak: "peak";
    readonly Boulder: "boulder";
    readonly Gypsum: "gypsum";
    readonly Zest: "zest";
};
