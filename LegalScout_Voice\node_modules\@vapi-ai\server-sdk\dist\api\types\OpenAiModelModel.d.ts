/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * This is the OpenAI model that will be used.
 */
export type OpenAiModelModel = "gpt-4.5-preview" | "chatgpt-4o-latest" | "o3-mini" | "o1-preview" | "o1-preview-2024-09-12" | "o1-mini" | "o1-mini-2024-09-12" | "gpt-4o-realtime-preview-2024-10-01" | "gpt-4o-realtime-preview-2024-12-17" | "gpt-4o-mini-realtime-preview-2024-12-17" | "gpt-4o-mini" | "gpt-4o-mini-2024-07-18" | "gpt-4o" | "gpt-4o-2024-05-13" | "gpt-4o-2024-08-06" | "gpt-4o-2024-11-20" | "gpt-4-turbo" | "gpt-4-turbo-2024-04-09" | "gpt-4-turbo-preview" | "gpt-4-0125-preview" | "gpt-4-1106-preview" | "gpt-4" | "gpt-4-0613" | "gpt-3.5-turbo" | "gpt-3.5-turbo-0125" | "gpt-3.5-turbo-1106" | "gpt-3.5-turbo-16k" | "gpt-3.5-turbo-0613";
export declare const OpenAiModelModel: {
    readonly Gpt45Preview: "gpt-4.5-preview";
    readonly Chatgpt4OLatest: "chatgpt-4o-latest";
    readonly O3Mini: "o3-mini";
    readonly O1Preview: "o1-preview";
    readonly O1Preview20240912: "o1-preview-2024-09-12";
    readonly O1Mini: "o1-mini";
    readonly O1Mini20240912: "o1-mini-2024-09-12";
    readonly Gpt4ORealtimePreview20241001: "gpt-4o-realtime-preview-2024-10-01";
    readonly Gpt4ORealtimePreview20241217: "gpt-4o-realtime-preview-2024-12-17";
    readonly Gpt4OMiniRealtimePreview20241217: "gpt-4o-mini-realtime-preview-2024-12-17";
    readonly Gpt4OMini: "gpt-4o-mini";
    readonly Gpt4OMini20240718: "gpt-4o-mini-2024-07-18";
    readonly Gpt4O: "gpt-4o";
    readonly Gpt4O20240513: "gpt-4o-2024-05-13";
    readonly Gpt4O20240806: "gpt-4o-2024-08-06";
    readonly Gpt4O20241120: "gpt-4o-2024-11-20";
    readonly Gpt4Turbo: "gpt-4-turbo";
    readonly Gpt4Turbo20240409: "gpt-4-turbo-2024-04-09";
    readonly Gpt4TurboPreview: "gpt-4-turbo-preview";
    readonly Gpt40125Preview: "gpt-4-0125-preview";
    readonly Gpt41106Preview: "gpt-4-1106-preview";
    readonly Gpt4: "gpt-4";
    readonly Gpt40613: "gpt-4-0613";
    readonly Gpt35Turbo: "gpt-3.5-turbo";
    readonly Gpt35Turbo0125: "gpt-3.5-turbo-0125";
    readonly Gpt35Turbo1106: "gpt-3.5-turbo-1106";
    readonly Gpt35Turbo16K: "gpt-3.5-turbo-16k";
    readonly Gpt35Turbo0613: "gpt-3.5-turbo-0613";
};
