/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface OpenAiTranscriber {
    /** This is the transcription provider that will be used. */
    provider: "openai";
    /** This is the model that will be used for the transcription. */
    model: Vapi.OpenAiTranscriberModel;
    /** This is the language that will be set for the transcription. */
    language?: Vapi.OpenAiTranscriberLanguage;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackTranscriberPlan;
}
