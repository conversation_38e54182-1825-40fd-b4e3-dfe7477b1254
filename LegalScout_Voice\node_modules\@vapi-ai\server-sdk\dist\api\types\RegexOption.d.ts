/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface RegexOption {
    /**
     * This is the type of the regex option. Options are:
     * - `ignore-case`: Ignores the case of the text being matched. Add
     * - `whole-word`: Matches whole words only.
     * - `multi-line`: Matches across multiple lines.
     */
    type: Vapi.RegexOptionType;
    /**
     * This is whether to enable the option.
     *
     * @default false
     */
    enabled: boolean;
}
