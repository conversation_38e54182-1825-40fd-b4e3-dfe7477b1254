/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the provider-specific ID that will be used.
 * Please note that ash, ballad, coral, sage, and verse may only be used with realtime models.
 */
export type OpenAiVoiceId = "alloy" | "echo" | "fable" | "onyx" | "nova" | "shimmer" | "ash" | "ballad" | "coral" | "sage" | "verse";
export declare const OpenAiVoiceId: {
    readonly Alloy: "alloy";
    readonly Echo: "echo";
    readonly Fable: "fable";
    readonly Onyx: "onyx";
    readonly Nova: "nova";
    readonly Shimmer: "shimmer";
    readonly Ash: "ash";
    readonly Ballad: "ballad";
    readonly Coral: "coral";
    readonly <PERSON>: "sage";
    readonly Verse: "verse";
};
