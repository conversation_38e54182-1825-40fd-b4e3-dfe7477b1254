/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface PlayHtVoice {
    /** This is the voice provider that will be used. */
    provider: "playht";
    /** This is the provider-specific ID that will be used. */
    voiceId: Vapi.PlayHtVoiceId;
    /** This is the speed multiplier that will be used. */
    speed?: number;
    /** A floating point number between 0, exclusive, and 2, inclusive. If equal to null or not provided, the model's default temperature will be used. The temperature parameter controls variance. Lower temperatures result in more predictable results, higher temperatures allow each run to vary more, so the voice may sound less like the baseline voice. */
    temperature?: number;
    /** An emotion to be applied to the speech. */
    emotion?: Vapi.PlayHtVoiceEmotion;
    /** A number between 1 and 6. Use lower numbers to reduce how unique your chosen voice will be compared to other voices. */
    voiceGuidance?: number;
    /** A number between 1 and 30. Use lower numbers to to reduce how strong your chosen emotion will be. Higher numbers will create a very emotional performance. */
    styleGuidance?: number;
    /** A number between 1 and 2. This number influences how closely the generated speech adheres to the input text. Use lower values to create more fluid speech, but with a higher chance of deviating from the input text. Higher numbers will make the generated speech more accurate to the input text, ensuring that the words spoken align closely with the provided text. */
    textGuidance?: number;
    /** Playht voice model/engine to use. */
    model?: Vapi.PlayHtVoiceModel;
    /** The language to use for the speech. */
    language?: Vapi.PlayHtVoiceLanguage;
    /** This is the plan for chunking the model output before it is sent to the voice provider. */
    chunkPlan?: Vapi.ChunkPlan;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackPlan;
}
