/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface OpenAiVoice {
    /** This is the voice provider that will be used. */
    provider: "openai";
    /**
     * This is the provider-specific ID that will be used.
     * Please note that ash, ballad, coral, sage, and verse may only be used with realtime models.
     */
    voiceId: Vapi.OpenAiVoiceId;
    /** This is the model that will be used for text-to-speech. */
    model?: Vapi.OpenAiVoiceModel;
    /**
     * This is a prompt that allows you to control the voice of your generated audio.
     * Does not work with 'tts-1' or 'tts-1-hd' models.
     */
    instructions?: string;
    /** This is the speed multiplier that will be used. */
    speed?: number;
    /** This is the plan for chunking the model output before it is sent to the voice provider. */
    chunkPlan?: Vapi.ChunkPlan;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackPlan;
}
