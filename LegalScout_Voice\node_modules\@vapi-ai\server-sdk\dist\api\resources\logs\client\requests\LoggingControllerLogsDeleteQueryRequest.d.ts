/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../../../../index";
export interface LoggingControllerLogsDeleteQueryRequest {
    /**
     * This is the type of the log.
     */
    type?: Vapi.LoggingControllerLogsDeleteQueryRequestType;
    assistantId?: string;
    /**
     * This is the ID of the phone number.
     */
    phoneNumberId?: string;
    /**
     * This is the ID of the customer.
     */
    customerId?: string;
    /**
     * This is the ID of the squad.
     */
    squadId?: string;
    /**
     * This is the ID of the call.
     */
    callId?: string;
}
