/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface OpenAiFunctionParameters {
    /** This must be set to 'object'. It instructs the model to return a JSON object containing the function call properties. */
    type: "object";
    /**
     * This provides a description of the properties required by the function.
     * JSON Schema can be used to specify expectations for each property.
     * Refer to [this doc](https://ajv.js.org/json-schema.html#json-data-type) for a comprehensive guide on JSON Schema.
     */
    properties: Record<string, Vapi.JsonSchema>;
    /** This specifies the properties that are required by the function. */
    required?: string[];
}
