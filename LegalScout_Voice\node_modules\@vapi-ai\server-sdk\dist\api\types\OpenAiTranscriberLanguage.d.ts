/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the language that will be set for the transcription.
 */
export type OpenAiTranscriberLanguage = "af" | "ar" | "hy" | "az" | "be" | "bs" | "bg" | "ca" | "zh" | "hr" | "cs" | "da" | "nl" | "en" | "et" | "fi" | "fr" | "gl" | "de" | "el" | "he" | "hi" | "hu" | "is" | "id" | "it" | "ja" | "kn" | "kk" | "ko" | "lv" | "lt" | "mk" | "ms" | "mr" | "mi" | "ne" | "no" | "fa" | "pl" | "pt" | "ro" | "ru" | "sr" | "sk" | "sl" | "es" | "sw" | "sv" | "tl" | "ta" | "th" | "tr" | "uk" | "ur" | "vi" | "cy";
export declare const OpenAiTranscriberLanguage: {
    readonly Af: "af";
    readonly Ar: "ar";
    readonly Hy: "hy";
    readonly Az: "az";
    readonly Be: "be";
    readonly Bs: "bs";
    readonly Bg: "bg";
    readonly Ca: "ca";
    readonly Zh: "zh";
    readonly Hr: "hr";
    readonly Cs: "cs";
    readonly Da: "da";
    readonly Nl: "nl";
    readonly En: "en";
    readonly Et: "et";
    readonly Fi: "fi";
    readonly Fr: "fr";
    readonly Gl: "gl";
    readonly De: "de";
    readonly El: "el";
    readonly He: "he";
    readonly Hi: "hi";
    readonly Hu: "hu";
    readonly Is: "is";
    readonly Id: "id";
    readonly It: "it";
    readonly Ja: "ja";
    readonly Kn: "kn";
    readonly Kk: "kk";
    readonly Ko: "ko";
    readonly Lv: "lv";
    readonly Lt: "lt";
    readonly Mk: "mk";
    readonly Ms: "ms";
    readonly Mr: "mr";
    readonly Mi: "mi";
    readonly Ne: "ne";
    readonly No: "no";
    readonly Fa: "fa";
    readonly Pl: "pl";
    readonly Pt: "pt";
    readonly Ro: "ro";
    readonly Ru: "ru";
    readonly Sr: "sr";
    readonly Sk: "sk";
    readonly Sl: "sl";
    readonly Es: "es";
    readonly Sw: "sw";
    readonly Sv: "sv";
    readonly Tl: "tl";
    readonly Ta: "ta";
    readonly Th: "th";
    readonly Tr: "tr";
    readonly Uk: "uk";
    readonly Ur: "ur";
    readonly Vi: "vi";
    readonly Cy: "cy";
};
