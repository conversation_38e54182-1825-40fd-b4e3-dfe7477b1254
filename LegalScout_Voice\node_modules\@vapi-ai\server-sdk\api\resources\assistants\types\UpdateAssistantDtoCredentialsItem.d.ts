/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../../../index";
export type UpdateAssistantDtoCredentialsItem = Vapi.CreateElevenLabsCredentialDto | Vapi.CreateAnthropicCredentialDto | Vapi.CreateAnyscaleCredentialDto | Vapi.CreateAssemblyAiCredentialDto | Vapi.CreateAzureOpenAiCredentialDto | Vapi.CreateAzureCredentialDto | Vapi.CreateByoSipTrunkCredentialDto | Vapi.CreateCartesiaCredentialDto | Vapi.CreateCerebrasCredentialDto | Vapi.CreateCloudflareCredentialDto | Vapi.CreateCustomLlmCredentialDto | Vapi.CreateDeepgramCredentialDto | Vapi.CreateDeepInfraCredentialDto | Vapi.CreateDeepSeekCredentialDto | Vapi.CreateGcpCredentialDto | Vapi.CreateGladiaCredentialDto | Vapi.CreateGoHighLevelCredentialDto | Vapi.CreateGoogleCredentialDto | Vapi.CreateGroqCredentialDto | Vapi.CreateInflectionAiCredentialDto | Vapi.CreateLangfuseCredentialDto | Vapi.CreateLmntCredentialDto | Vapi.CreateMakeCredentialDto | Vapi.CreateOpenAiCredentialDto | Vapi.CreateOpenRouterCredentialDto | Vapi.CreatePerplexityAiCredentialDto | Vapi.CreatePlayHtCredentialDto | Vapi.CreateRimeAiCredentialDto | Vapi.CreateRunpodCredentialDto | Vapi.CreateS3CredentialDto | Vapi.CreateSupabaseCredentialDto | Vapi.CreateSmallestAiCredentialDto | Vapi.CreateTavusCredentialDto | Vapi.CreateTogetherAiCredentialDto | Vapi.CreateTwilioCredentialDto | Vapi.CreateVonageCredentialDto | Vapi.CreateWebhookCredentialDto | Vapi.CreateXAiCredentialDto | Vapi.CreateNeuphonicCredentialDto | Vapi.CreateHumeCredentialDto | Vapi.CreateMistralCredentialDto | Vapi.CreateSpeechmaticsCredentialDto | Vapi.CreateTrieveCredentialDto | Vapi.CreateGoogleCalendarOAuth2ClientCredentialDto | Vapi.CreateGoogleCalendarOAuth2AuthorizationCredentialDto | Vapi.CreateGoogleSheetsOAuth2AuthorizationCredentialDto | Vapi.CreateSlackOAuth2AuthorizationCredentialDto;
