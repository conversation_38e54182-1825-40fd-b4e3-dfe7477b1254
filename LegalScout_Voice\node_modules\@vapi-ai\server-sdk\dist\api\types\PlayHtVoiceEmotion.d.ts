/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * An emotion to be applied to the speech.
 */
export type PlayHtVoiceEmotion = "female_happy" | "female_sad" | "female_angry" | "female_fearful" | "female_disgust" | "female_surprised" | "male_happy" | "male_sad" | "male_angry" | "male_fearful" | "male_disgust" | "male_surprised";
export declare const PlayHtVoiceEmotion: {
    readonly FemaleHappy: "female_happy";
    readonly FemaleSad: "female_sad";
    readonly <PERSON>Angry: "female_angry";
    readonly FemaleFearful: "female_fearful";
    readonly FemaleDisgust: "female_disgust";
    readonly FemaleSurprised: "female_surprised";
    readonly <PERSON>Happy: "male_happy";
    readonly MaleSad: "male_sad";
    readonly <PERSON>Angry: "male_angry";
    readonly <PERSON>Fearful: "male_fearful";
    readonly MaleDisgust: "male_disgust";
    readonly <PERSON>Surprised: "male_surprised";
};
