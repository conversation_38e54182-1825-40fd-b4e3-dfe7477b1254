/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * This is the mode for the first message. Default is 'assistant-speaks-first'.
 *
 * Use:
 * - 'assistant-speaks-first' to have the assistant speak first.
 * - 'assistant-waits-for-user' to have the assistant wait for the user to speak first.
 * - 'assistant-speaks-first-with-model-generated-message' to have the assistant speak first with a message generated by the model based on the conversation state. (`assistant.model.messages` at call start, `call.messages` at squad transfer points).
 *
 * @default 'assistant-speaks-first'
 */
export type UpdateAssistantDtoFirstMessageMode = "assistant-speaks-first" | "assistant-speaks-first-with-model-generated-message" | "assistant-waits-for-user";
export declare const UpdateAssistantDtoFirstMessageMode: {
    readonly AssistantSpeaksFirst: "assistant-speaks-first";
    readonly AssistantSpeaksFirstWithModelGeneratedMessage: "assistant-speaks-first-with-model-generated-message";
    readonly <PERSON><PERSON>ait<PERSON><PERSON>or<PERSON>ser: "assistant-waits-for-user";
};
