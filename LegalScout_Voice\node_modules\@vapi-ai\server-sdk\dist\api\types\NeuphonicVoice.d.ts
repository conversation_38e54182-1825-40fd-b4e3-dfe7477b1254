/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface NeuphonicVoice {
    /** This is the voice provider that will be used. */
    provider: "neuphonic";
    /** This is the provider-specific ID that will be used. */
    voiceId: string;
    /** This is the model that will be used. Defaults to 'neu_fast' if not specified. */
    model?: Vapi.NeuphonicVoiceModel;
    /** This is the language (ISO 639-1) that is enforced for the model. */
    language: Record<string, unknown>;
    /** This is the speed multiplier that will be used. */
    speed?: number;
    /** This is the plan for chunking the model output before it is sent to the voice provider. */
    chunkPlan?: Vapi.ChunkPlan;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackPlan;
}
