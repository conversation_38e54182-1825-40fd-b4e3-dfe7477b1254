/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface RimeAiVoice {
    /** This is the voice provider that will be used. */
    provider: "rime-ai";
    /** This is the provider-specific ID that will be used. */
    voiceId: Vapi.RimeAiVoiceId;
    /** This is the model that will be used. Defaults to 'v1' when not specified. */
    model?: Vapi.RimeAiVoiceModel;
    /** This is the speed multiplier that will be used. */
    speed?: number;
    /** This is a flag that controls whether to add slight pauses using angle brackets. Example: “Hi. <200> I’d love to have a conversation with you.” adds a 200ms pause between the first and second sentences. */
    pauseBetweenBrackets?: boolean;
    /** This is a flag that controls whether text inside brackets should be phonemized (converted to phonetic pronunciation) - Example: "{h'El.o} World" will pronounce "Hello" as expected. */
    phonemizeBetweenBrackets?: boolean;
    /** This is a flag that controls whether to optimize for reduced latency in streaming. https://docs.rime.ai/api-reference/endpoint/websockets#param-reduce-latency */
    reduceLatency?: boolean;
    /** This is a string that allows inline speed control using alpha notation. https://docs.rime.ai/api-reference/endpoint/websockets#param-inline-speed-alpha */
    inlineSpeedAlpha?: string;
    /** This is the plan for chunking the model output before it is sent to the voice provider. */
    chunkPlan?: Vapi.ChunkPlan;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackPlan;
}
