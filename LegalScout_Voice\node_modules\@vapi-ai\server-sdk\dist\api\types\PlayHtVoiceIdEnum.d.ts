/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type PlayHtVoiceIdEnum = "jennifer" | "melissa" | "will" | "chris" | "matt" | "jack" | "ruby" | "davis" | "donna" | "michael";
export declare const PlayHtVoiceIdEnum: {
    readonly <PERSON>: "jennifer";
    readonly <PERSON>: "melissa";
    readonly <PERSON>: "will";
    readonly <PERSON>: "chris";
    readonly <PERSON>: "matt";
    readonly <PERSON>: "jack";
    readonly <PERSON>: "ruby";
    readonly <PERSON>: "davis";
    readonly <PERSON>: "donna";
    readonly <PERSON>: "michael";
};
