/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The language to use for the speech.
 */
export type PlayHtVoiceLanguage = "afrikaans" | "albanian" | "amharic" | "arabic" | "bengali" | "bulgarian" | "catalan" | "croatian" | "czech" | "danish" | "dutch" | "english" | "french" | "galician" | "german" | "greek" | "hebrew" | "hindi" | "hungarian" | "indonesian" | "italian" | "japanese" | "korean" | "malay" | "mandarin" | "polish" | "portuguese" | "russian" | "serbian" | "spanish" | "swedish" | "tagalog" | "thai" | "turkish" | "ukrainian" | "urdu" | "xhosa";
export declare const PlayHtVoiceLanguage: {
    readonly Afrikaans: "afrikaans";
    readonly Albanian: "albanian";
    readonly Amharic: "amharic";
    readonly Arabic: "arabic";
    readonly Bengali: "bengali";
    readonly Bulgarian: "bulgarian";
    readonly Catalan: "catalan";
    readonly Croatian: "croatian";
    readonly Czech: "czech";
    readonly Danish: "danish";
    readonly Dutch: "dutch";
    readonly English: "english";
    readonly French: "french";
    readonly Galician: "galician";
    readonly German: "german";
    readonly Greek: "greek";
    readonly Hebrew: "hebrew";
    readonly Hindi: "hindi";
    readonly Hungarian: "hungarian";
    readonly Indonesian: "indonesian";
    readonly Italian: "italian";
    readonly Japanese: "japanese";
    readonly Korean: "korean";
    readonly Malay: "malay";
    readonly Mandarin: "mandarin";
    readonly Polish: "polish";
    readonly Portuguese: "portuguese";
    readonly Russian: "russian";
    readonly Serbian: "serbian";
    readonly Spanish: "spanish";
    readonly Swedish: "swedish";
    readonly Tagalog: "tagalog";
    readonly Thai: "thai";
    readonly Turkish: "turkish";
    readonly Ukrainian: "ukrainian";
    readonly Urdu: "urdu";
    readonly Xhosa: "xhosa";
};
